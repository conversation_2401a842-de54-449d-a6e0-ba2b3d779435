import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

export interface GalacticSuccessConfig {
  title: string;
  subtitle: string;
  badgeText: string;
  badgeIcon?: string;
  equipmentItems: string[];
  showDestinationName?: boolean;
  destinationName?: string;
  customIconClass?: string;
  theme?: 'success' | 'primary' | 'warning' | 'info';
}

@Component({
  selector: 'app-galactic-success-card',
  imports: [CommonModule],
  templateUrl: './galactic-success-card.component.html',
  styleUrl: './galactic-success-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GalacticSuccessCardComponent {
  /**
   * Configuration object for the galactic success card
   */
  @Input({ required: true }) config!: GalacticSuccessConfig;

  /**
   * Whether to show the animated orbital ring
   */
  @Input() showOrbitalRing: boolean = true;

  /**
   * Whether to show the cosmic glow effect
   */
  @Input() showCosmicGlow: boolean = true;

  /**
   * Custom CSS classes to apply to the container
   */
  @Input() customClasses: string = '';

  /**
   * Whether to enable animations
   */
  @Input() enableAnimations: boolean = true;

  /**
   * Size variant for the component
   */
  @Input() size: 'small' | 'medium' | 'large' = 'medium';

  /**
   * Gets the icon class for the success icon
   */
  getIconClass(): string {
    if (this.config.customIconClass) {
      return this.config.customIconClass;
    }
    
    switch (this.config.theme) {
      case 'success':
        return 'pi pi-check';
      case 'primary':
        return 'pi pi-star-fill';
      case 'warning':
        return 'pi pi-exclamation-triangle';
      case 'info':
        return 'pi pi-info-circle';
      default:
        return 'pi pi-check';
    }
  }

  /**
   * Gets the badge icon class
   */
  getBadgeIcon(): string {
    return this.config.badgeIcon || 'pi pi-star-fill';
  }

  /**
   * Gets the container CSS classes
   */
  getContainerClasses(): string {
    const baseClasses = 'galactic-success-container';
    const sizeClass = `size-${this.size}`;
    const themeClass = `theme-${this.config.theme || 'success'}`;
    const animationClass = this.enableAnimations ? 'animations-enabled' : 'animations-disabled';
    
    return `${baseClasses} ${sizeClass} ${themeClass} ${animationClass} ${this.customClasses}`.trim();
  }
}
