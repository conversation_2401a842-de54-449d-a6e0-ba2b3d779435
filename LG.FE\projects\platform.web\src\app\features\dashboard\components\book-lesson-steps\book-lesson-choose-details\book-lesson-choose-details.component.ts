import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, signal, type OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { StepperModule } from 'primeng/stepper';
import { DefaultGetStudentsRequest, EmitEvent, EventBusService, Events, Severity } from 'SharedModules.Library';
import { DataApiStateService } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { CardSplitLayoutComponent } from '@platform.src/app/shared/components/card-split-layout/card-split-layout.component';
import { EmptyDataImageTextComponent } from '@platform.src/app/shared/components/empty-data-image-text/empty-data-image-text.component';

import { PrimeStudentGroupSelectionComponent } from '@platform.src/app/shared/components/prime/prime-student-group-selection/prime-student-group-selection.component';
import { PrimeStudentsSelectionComponent } from '@platform.src/app/shared/components/prime/prime-students-selection/prime-students-selection.component';
import { BottomBarStepButtonsComponent } from '@platform.src/app/features/dashboard/bottom-bar-step-buttons/bottom-bar-step-buttons.component';
import { PackageMiniInfoCardComponent } from '@platform.app/shared/dashboard/package-mini-info-card/package-mini-info-card.component';

@Component({
  selector: 'app-book-lesson-choose-details',
  imports: [CommonModule,
    ButtonModule,
    CardSplitLayoutComponent,
    RouterModule,
    StepperModule,
    CardSplitLayoutComponent,
    EmptyDataImageTextComponent,
    BottomBarStepButtonsComponent,
    PrimeStudentsSelectionComponent,
    PrimeStudentGroupSelectionComponent,
    PackageMiniInfoCardComponent,
  ],
  templateUrl: './book-lesson-choose-details.component.html',
  styleUrl: './book-lesson-choose-details.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BookLessonChooseDetailsComponent implements OnInit {

  Severity = Severity;
  resetStudentsSelectionSignal = signal(false);
  generalService = inject(GeneralService);
  dataStateService = inject(DataApiStateService);
  private readonly eventBusService = inject(EventBusService);
  carouselItems: any[] = this.generalService.dummyPackages;
  selectedCardId: number | null = null;

  students$ = computed(() => this.dataStateService.parentStudents.state() || {});
  studentGroups$ = computed(() => this.dataStateService.parentStudentsGroups.state());
  ngOnInit(): void {
    // DefaultGetStudentsRequest automatically cleans null values in constructor
    const request = new DefaultGetStudentsRequest();
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudents, request));
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudentsGroups, undefined));
  }


  /** Handles selection of a student */
  onSelectedStudent(student: any): void {
  }


  onPackageSelected(index: number): void {
    this.selectedCardId = this.selectedCardId === index ? null : index;
    console.log(index);
  }

}
