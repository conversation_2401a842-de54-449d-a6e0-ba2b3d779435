import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, type OnInit } from '@angular/core';
import { Observable, Subscription, catchError, filter, fromEvent, map, mapTo, merge, of, pairwise, startWith, switchMap, tap, throwError } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import {
  IApiResponseBase,
  IBasicProfileInfoDto,
  IBasket,
  IdentityRoutes,
  IGenderEnum,
  IGetBasketResponse,
  IGetCountriesResponse,
  IGetDialCodesResponse,
  IGetLanguagesResponse,
  IGetPricesByLanguageResponse,
  IGetProfileInfoRequest,
  IGetProfileInfoResponse,
  IGetStudentDashboardRequest,
  IGetStudentDashboardResponse,
  IGetStudentGroupsResponse,
  IGetStudentsRequest,
  IGetStudentsResponse,
  IUserClaims,
  IGetTimezonesResponse,
  ImpersonateStudentRequest,
  IParentDashboardHandlerResponse,
  IParents,
  IpGeolocation,
  IpGeoResponse,
  IProfileInfo,
  IStopImpersonateStudentRequest,
  IStudents,
  LocationDataRoutes,
  StudentGroupRoutes
} from '../../GeneratedTsFiles';
import { DeviceKind, IUserRole } from '../../models/general.model';
import { DataApiStateService, State } from '../../services/data-api-state.service';
import {
  EventBusService,
  Events,
  StateLoadDashboardData,
  StateLoadStartImpersonateEventData,
  StateLoadStopImpersonateEventData,
  UserProfileInfoEventData,
  UserEventData,
  DefaultGetStudentsRequest
} from '../../services/event-bus.service';
import { ToastService } from '../../services/toast.service';
import { untilDestroyed } from '../../helpers/until-destroyed'; // Custom untilDestroyed helper
import { GeneralService } from '../../services/general.service';
import { HandleApiResponseService, GEO_LOCATION_CONFIG } from '../../services/handle-api-response.service';
import { AuthStateService } from '../../services/auth-state.service';
import { getToastMessage, ToastMessages } from '../../models/toast-messages';
import { Router } from '@angular/router';
import { ParentService } from '../../services/parent.service';

@Component({
  selector: 'lib-state-api-calls',
  standalone: true, // Mark as standalone for better modularity
  imports: [
    CommonModule,
  ],
  template: ``, // This component has no UI, so an empty template is fine
  styleUrl: './state-api-calls.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush // Optimize change detection
})
export class StateApiCallsComponent implements OnInit {
  // Injected Services (private if not used in template)
  private router = inject(Router);
  private apiService = inject(HandleApiResponseService);
  private generalService = inject(GeneralService);
  private dataStateService = inject(DataApiStateService);
  private eventBusService = inject(EventBusService);
  private authService = inject(AuthStateService);
  private parentService = inject(ParentService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef); // Angular's DestroyRef for takeUntilDestroyed

  // Internal state/helpers
  private subscriptions: Subscription[] = []; // For subscriptions not managed by takeUntilDestroyed
  private untilDestroyed = untilDestroyed(); // Custom helper for untilDestroyed
  private previousOnline = navigator.onLine; // Tracks previous online status for network changes

  // Signal for user claims, derived from AuthStateService
  private userClaimsSignal = this.authService.getUserClaims();
  public user = computed(() => this.userClaimsSignal); // Exposed as computed for potential external use

  // Using a direct subscription for online/offline status, managed by takeUntilDestroyed
  private onlineStatusSubscription: Subscription | null = null; // Initialize to null

  ngOnInit(): void {
    this.initEventHandlers();
    this.subscribeToNetworkStatus();
    this.handleDeviceChanges();
  }

  ngOnDestroy(): void {
    // Ensure all subscriptions are unsubscribed on component destruction
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
    this.onlineStatusSubscription?.unsubscribe(); // Unsubscribe explicit online status subscription
  }

  /**
   * Initializes event bus listeners and maps them to respective API loading methods.
   * This centralizes event handling for state updates.
   */
  private initEventHandlers(): void {
    const eventHandlers = [
      { event: Events.StateLoadTeachingLanguages, handler: () => this.loadLanguages() },
      { event: Events.StateLoadParentStudents, handler: (payload: DefaultGetStudentsRequest) => this.loadParentStudents(payload) },
      { event: Events.StateLoadParentStudentsGroups, handler: () => this.loadParentStudentsGroups() },
      { event: Events.StateLoadStartImpersonate, handler: (payload: StateLoadStartImpersonateEventData) => this.loadStartImpersonate(payload) },
      { event: Events.StateLoadStopImpersonate, handler: (payload: StateLoadStopImpersonateEventData) => this.loadStopImpersonate(payload) },
      { event: Events.StudentGroupAdded, handler: () => this.loadParentStudentsGroups() }, // Reload groups on add
      { event: Events.StudentGroupEdited, handler: () => this.loadParentStudentsGroups() }, // Reload groups on edit
      { event: Events.StateLoadGetBasket, handler: () => this.loadGetBasket() },
      { event: Events.StateLoadGeoLocationData, handler: () => this.loadGeoLocationData() },
      { event: Events.StateLoadCountries, handler: () => this.loadCountries() },
      { event: Events.StateLoadTimezones, handler: () => this.loadTimezones() },
      { event: Events.StateLoadDialCodes, handler: () => this.loadDialCodes() },
      { event: Events.StateLoadParentDashboard, handler: (payload: StateLoadDashboardData) => this.loadDashboard(payload) },
      { event: Events.StateLoadNativeLanguages, handler: () => this.loadLocationNativeLanguages() },
      { event: Events.StateLoadProfileInfo, handler: (payload: UserProfileInfoEventData) => this.loadProfileInfo(payload) },
      { event: Events.UserLoggedIn, handler: (payload: UserEventData) => this.initLoggedInUser(payload) },
    ];

    eventHandlers.forEach(({ event, handler }) => {
      const subscription = this.eventBusService.on(event, (payload: any) => handler(payload));
      this.subscriptions.push(subscription);
    });
  }

  /**
   * Loads teaching languages and updates the state.
   */
  private loadLanguages(): void {
    this.handleStateApiCall(
      this.apiService.getTeachingLanguages(),
      this.dataStateService.teachingLanguages.setState
    ).subscribe();
  }

  /**
   * Loads students associated with the current parent user and updates the state.
   */
  private loadParentStudents(payload: DefaultGetStudentsRequest): void {
    const parentId = this.authService.getUserClaims()?.id; //
    if (!parentId) {
      console.warn('Parent ID not found. Cannot load parent students.');
      return;
    }

    // Ensure parentId is set in the request if not already provided
    const request = { ...payload };
    if (!request.parentId) {
      request.parentId = parentId;
    }

    this.dataStateService.setStateDirectly(
      this.dataStateService.parentStudents.setState,
      { data: null, loading: true, error: null, hasError: false }
    );

    console.log('loadParentStudents request', request);

    this.apiService.getApiData(
      {
        url: IStudents.getStudents,
        method: 'GET'
      },
      request
    ).subscribe({
      next: (response) => {
        this.dataStateService.setStateDirectly(
          this.dataStateService.parentStudents.setState,
          { data: response, loading: false, error: null, hasError: false }
        );
      }
    });

  }

  /**
   * Loads student groups for the current parent user and updates the state.
   */
  private loadParentStudentsGroups(): void {
    const parentId = this.authService.getUserClaims()?.id; //
    if (!parentId) {
      console.warn('Parent ID not found. Cannot load parent student groups.');
      return;
    }

    this.handleStateApiCall(
      this.apiService.getApiData<IGetStudentGroupsResponse>({
        url: StudentGroupRoutes.getAllForParent,
        method: 'GET'
      }, { parentId }).pipe(
        map(response => response.pageData)
      ),
      this.dataStateService.parentStudentsGroups.setState
    ).subscribe();
  }

  /**
   * Initiates the impersonation of a student.
   * Handles API calls for impersonation, dashboard data, and profile info.
   * @param payload Data required for impersonation.
   */
  private loadStartImpersonate(payload: StateLoadStartImpersonateEventData): void {
    this.generalService.showDivLoading();
    this.generalService.userPhotoTopbarToggle.set(true); //

    this.apiService.getApiData(
      { url: IdentityRoutes.postImpersonateStudent, method: 'POST' },
      <ImpersonateStudentRequest>{
        impersonateStudentId: payload.impersonateStudentId,
        parentRefreshToken: this.authService.getRefreshToken() as string //
      }
    ).pipe(
      switchMap(impersonateResponse => {
        this.authService.handleUserDataAndDecodeJWT(impersonateResponse as { token: string; refreshToken: string; userDto?: IUserClaims }); //
        // Fetch student dashboard after successful impersonation
        return this.apiService.getApiData<IGetStudentDashboardResponse>(
          { url: IStudents.getStudentDashboard, method: 'GET' },
          { studentId: payload.impersonateStudentId } as IGetStudentDashboardRequest //
        );
      }),
      switchMap(() => { // Dashboard response is not directly used here, but ensures sequential fetch
        // Fetch detailed profile info for the impersonated student
        return this.apiService.getApiData<IGetProfileInfoResponse>(
          { url: IProfileInfo.getProfileInfo, method: 'GET' },
          { userId: payload.impersonateStudentId } as IGetProfileInfoRequest //
        );
      }),
      takeUntilDestroyed(this.destroyRef) // Use Angular's built-in takeUntilDestroyed
    ).subscribe({
      next: profileResponse => {
        this.authService.setUserProfileInfo(profileResponse); //
        this.authService.setUserBasicInfo(profileResponse.basicProfileInfoDto as IBasicProfileInfoDto); //

        this.dataStateService.setStateDirectly(
          this.dataStateService.getUserProfileInfo.setState,
          {
            data: profileResponse,
            loading: false,
            error: null,
            hasError: false
          }
        );

        this.generalService.hideDivLoading();
        this.generalService.navigateToYoungsterDashboard();
        this.toastService.show(
          getToastMessage(ToastMessages.ParentImpersonateStarted.success, {
            data: this.authService.getUserBasicInfo()?.firstName! //
          })
        );

        setTimeout(() => {
          this.generalService.userPhotoTopbarToggle.set(false); //
        }, 30);
      },
      error: err => {
        this.generalService.hideDivLoading(); // Ensure loading indicator is hidden on error

        if (err.messages) {
          const errorMessage = err.messages.join(', ');
          this.toastService.showError(
            getToastMessage(ToastMessages.StateApiMessage.error, { data: errorMessage })
          );
        } else {
          this.toastService.showError('Failed to start impersonation.'); // Generic error message
        }
        console.error('Error starting impersonation:', err); // Log the full error
      }
    });
  }

  /**
   * Stops the impersonation of a student.
   * Handles API call to stop impersonation and redirects to parent dashboard.
   * @param payload Data required to stop impersonation.
   */
  private loadStopImpersonate(payload: IStopImpersonateStudentRequest): void {
    this.generalService.showDivLoading();
    this.generalService.userPhotoTopbarToggle.set(true); //

    this.apiService.getApiData(
      { url: IdentityRoutes.postStopImpersonateStudent, method: 'POST' },
      <IStopImpersonateStudentRequest>{ studentRefreshToken: payload.studentRefreshToken } //
    ).pipe(
      switchMap(stopImpersonateResponse => {
        this.authService.handleUserDataAndDecodeJWT(stopImpersonateResponse as { token: string; refreshToken: string }); // Update auth state
        const user = this.authService.getUserClaims(); //
        if (!user) {
          return throwError(() => new Error('User claims not found after stopping impersonation.'));
        }
        const { url, urlParams } = this.getDashboardConfig(user.role, user.id); //

        // Reload parent dashboard data
        return this.handleStateApiCall(
          this.apiService.getApiData<IParentDashboardHandlerResponse>({ url, method: 'GET' }, urlParams),
          this.dataStateService.getParentDashboard.setState
        );
      }),
      takeUntilDestroyed(this.destroyRef) // Use Angular's built-in takeUntilDestroyed
    ).subscribe({
      next: (response: IParentDashboardHandlerResponse) => {
        this.authService.setUserProfileInfo(response as IParentDashboardHandlerResponse); //
        this.authService.setUserBasicInfo(response.basicProfileInfoDto as IBasicProfileInfoDto); //
        this.apiService.clearAllCaches(); // Clear API caches
        this.authService.goToDashboardPerRole(); //
        this.generalService.hideDivLoading();
        this.toastService.show(ToastMessages.ParentImpersonateStopped.success);

        setTimeout(() => {
          this.generalService.userPhotoTopbarToggle.set(false); //
        }, 30);
      },
      error: err => {
        this.generalService.hideDivLoading(); // Ensure loading indicator is hidden on error
        console.error('Error stopping impersonation:', err); // Log the full error
        this.toastService.showError('Failed to stop impersonation.'); // Generic error message
      }
    });
  }

  /**
   * Loads the basket information for the current parent and updates the state.
   */
  private loadGetBasket(): void {
    const parentId = this.authService.getUserBasicInfo()?.userId; //
    if (!parentId) {
      console.warn('Parent User ID not found. Cannot load basket.');
      return;
    }

    this.handleStateApiCall(
      this.apiService.getApiData<IGetBasketResponse>({
        url: IBasket.getBasket,
        method: 'GET'
      }, { parentId }),
      this.dataStateService.getBasket.setState
    ).subscribe();
  }

  /**
   * Loads country data and updates the state.
   */
  private loadCountries(): void {
    this.handleStateApiCall(
      this.apiService.getApiData<IGetCountriesResponse>({
        url: LocationDataRoutes.getCountries,
        method: 'GET'
      }),
      this.dataStateService.getCountries.setState
    ).subscribe();
  }

  /**
   * Loads timezone data and updates the state.
   */
  private loadTimezones(): void {
    this.handleStateApiCall(
      this.apiService.getApiData<IGetTimezonesResponse>({
        url: LocationDataRoutes.getTimezones,
        method: 'GET'
      }, null), // No specific parameters needed for this GET call
      this.dataStateService.getTimezones.setState
    ).subscribe();
  }

  /**
   * Loads dial code data and updates the state.
   */
  private loadDialCodes(): void {
    this.handleStateApiCall(
      this.apiService.getApiData<IGetDialCodesResponse>({
        url: LocationDataRoutes.getDialCodes,
        method: 'GET'
      }, null), // No specific parameters needed for this GET call
      this.dataStateService.getDialCodes.setState
    ).subscribe();
  }

  /**
   * Loads profile information for a user and updates the state.
   * Handles special cases for parents viewing student profiles and incomplete registration.
   * @param payload Contains userId and a flag for password setting.
   */
  private loadProfileInfo(payload: UserProfileInfoEventData): void {
    const currentUser = this.authService.getUserClaims(); //
    if (!currentUser) {
      console.warn('Current user claims not found. Cannot load profile info.');
      return;
    }

    const userIdToFetch = payload.userId || currentUser.id; //

    this.handleStateApiCall(
      this.apiService.getApiData<IGetProfileInfoResponse>({
        url: IProfileInfo.getProfileInfo,
        method: 'GET'
      }, { UserId: userIdToFetch }), // Ensure correct parameter casing
      this.dataStateService.getUserProfileInfo.setState
    ).subscribe({
      next: (response: IGetProfileInfoResponse) => {
        // Handle current user's profile info
        if (currentUser.id === response.basicProfileInfoDto.userId) { //
          this.setUserProfileData(response); //

          if (payload.hasSetPassword) { //
            this.authService.setHasSetPasswordOneTime({ setPasswordOneTime: true }); //
            this.router.navigate(['/set-password-first-time']); //
            return;
          }
          this.authService.checkIncompleteRegistration(response.basicProfileInfoDto); //
        } else if (currentUser.role === IUserRole.PARENT && response.basicProfileInfoDto.discriminator === IUserRole.STUDENT) { //
          // Handle parent viewing student's profile
          this.parentService.currentStudent.set(response); //
          this.parentService.currentStudentId.set(response.basicProfileInfoDto.userId); //
          this.dataStateService.setStateDirectly(
            this.dataStateService.getUserProfileInfo.setState,
            { data: response, loading: false, error: null, hasError: false }
          );
        } else if (currentUser.role === IUserRole.ADMIN) { //
          // Handle admin viewing any profile
          this.dataStateService.setStateDirectly(
            this.dataStateService.getUserProfileInfo.setState,
            { data: response, loading: false, error: null, hasError: false }
          );
        }
      },
      error: err => {
        console.error('Error loading profile info:', err);
        this.toastService.showError('Failed to load profile information.');
      }
    });
  }

  /**
   * Loads native languages data and updates the state.
   */
  private loadLocationNativeLanguages(): void {
    this.handleStateApiCall(
      this.apiService.getApiData<IGetLanguagesResponse>({
        url: LocationDataRoutes.getLanguages,
        method: 'GET'
      }),
      this.dataStateService.getNativeLanguages.setState
    ).subscribe();
  }

  /**
   * Loads geo-location data and updates the state.
   */
  private loadGeoLocationData(): void {
    this.handleStateApiCall(
      this.apiService.getApiData<IpGeoResponse>(
        { url: IpGeolocation.post_IpGeolocation, method: 'POST' },
        GEO_LOCATION_CONFIG,
        false // Do not show default loading spinner for this specific call if desired
      ),
      this.dataStateService.getGeoLocationData.setState,
      undefined,
      false // Do not handle errors with toast for this specific call if desired
    ).subscribe();
  }

  /**
   * Generic handler for API calls that update a specific state.
   * Provides consistent loading, success, and error handling.
   * @param apiCall The Observable representing the API request.
   * @param setState The state setter function from DataApiStateService.
   * @param onSuccess Optional callback function to execute on successful API response.
   * @param handleError Flag to determine if default error toast should be shown.
   * @returns An Observable that can be subscribed to.
   */
  private handleStateApiCall<T>(
    apiCall: Observable<T>,
    setState: (data: State<T>) => void,
    onSuccess?: (response: T) => void,
    handleError: boolean = true
  ): Observable<T> {
    return this.dataStateService
      .handleApiCall(apiCall, setState, 0, false) // `0` for debounceTime, `false` for showDefaultLoading
      .pipe(
        takeUntilDestroyed(this.destroyRef), // Use Angular's built-in takeUntilDestroyed
        tap(response => {
          if (onSuccess) {
            onSuccess(response);
          }
        }),
      );
  }

  /**
   * Subscribes to network status changes (online/offline) and shows toasts accordingly.
   */
  private subscribeToNetworkStatus(): void {
    this.onlineStatusSubscription = merge(
      of(navigator.onLine), // Initial status
      fromEvent(window, 'online').pipe(mapTo(true)),
      fromEvent(window, 'offline').pipe(mapTo(false))
    ).pipe(
      startWith(this.previousOnline), // Start with current status
      pairwise(), // Get [previous, current] values
      takeUntilDestroyed(this.destroyRef) // Automatically unsubscribe
    ).subscribe(([prev, curr]) => {
      // console.log('Online status changed:', curr); // Keep for debugging if needed
      if (curr && !prev) {
        this.toastService.show(ToastMessages.InternetConnection.success); //
      } else if (!curr && prev) {
        this.toastService.showError(ToastMessages.InternetConnection.error.detail, ToastMessages.InternetConnection.error.summary); // Use showError
      }
      this.previousOnline = curr;
    });
  }

  /**
   * Handles device kind changes and adjusts UI settings (e.g., sidebar).
   */
  private handleDeviceChanges(): void {
    this.generalService.deviceKind.pipe(
      takeUntilDestroyed(this.destroyRef),
      tap((deviceKind: DeviceKind) => {
        this.generalService.deviceIs.set(deviceKind); // Update device state
        if (deviceKind.w1200down) {
          this.generalService.setMiniLayoutSidebar(false); // Adjust layout for smaller screens
        }
      })
    ).subscribe();
  }

  /**
   * Initializes user data upon successful login, fetches profile info,
   * and handles redirects for password setting or dashboard.
   * @param payload User event data, including whether password needs to be set.
   */
  private initLoggedInUser(payload: UserEventData): void {
    const user = this.authService.getUserClaims(); //
    if (!user) {
      console.warn('User claims not found on login initialization.');
      return;
    }

    this.generalService.showDivLoading();
    this.handleStateApiCall(
      this.apiService.getApiData<IGetProfileInfoResponse>({
        url: IProfileInfo.getProfileInfo,
        method: 'GET'
      }, { userId: user.id }),
      this.dataStateService.getUserProfileInfo.setState,
      (response: IGetProfileInfoResponse) => {
        this.setUserProfileData(response); //
        setTimeout(() => {
          this.authService.removeLoginFormState(); // Clear login form state
          this.authService.removeOtpResendTimerKey(); // Clear OTP timer

          if (payload.hasSetPassword) { //
            this.authService.setHasSetPasswordOneTime({ setPasswordOneTime: true }); //
            this.router.navigate(['/set-password-first-time']); //
          } else if (payload.redirectUrl) { //
            this.router.navigate([payload.redirectUrl, { fromLogin: 'true' }]); //
          } else {
            this.authService.goToDashboardPerRole({ fromLogin: 'true' }); //
          }
        }, 20); // Small delay to ensure state updates
      }
    ).subscribe(); // Explicitly subscribe to trigger the call
  }

  /**
   * Loads the appropriate dashboard data based on the user's role.
   * @param payload Optional data, including a flag to get more logged-in user info.
   */
  private loadDashboard(payload?: StateLoadDashboardData): void {
    const shouldGetLoggedInInfo = payload?.shouldGetMoreData || false; //
    const user = this.authService.getUserClaims(); //
    if (!user || user.role === IUserRole.ADMIN) { //
      return; // Admins might have a different dashboard loading mechanism
    }

    const { url, urlParams } = this.getDashboardConfig(user.role, user.id); //

    this.apiService.getApiData<IParentDashboardHandlerResponse>(
      { url: url, method: 'GET' }, //
      urlParams //
    ).pipe(
      switchMap(dashboardResponse => {
        this.authService.setUserBasicInfo(dashboardResponse.basicProfileInfoDto); //
        this.dataStateService.setStateDirectly(
          this.dataStateService.getParentDashboard.setState,
          {
            data: dashboardResponse,
            loading: false,
            error: null,
            hasError: false,
          }
        );
        // If more logged-in info is needed, trigger that flow
        // The original code had a commented out initLoggedInUser call.
        // If this logic is needed, it should be re-evaluated for how it fits
        // with the overall login/dashboard flow to prevent infinite loops or redundant calls.
        if (shouldGetLoggedInInfo) {
          // This call is commented out in the original, re-enable if logic requires.
          // this.initLoggedInUser({ user: user, hasSetPassword: false });
        }
        return of(null); // Return observable to complete the stream
      }),
      takeUntilDestroyed(this.destroyRef) // Automatically unsubscribe
    ).subscribe({
      next: () => {
        // console.log('Dashboard data loaded successfully.');
        this.generalService.hideDivLoading(); // Hide loading after dashboard data is processed
      },
      error: (err: IApiResponseBase) => {
        console.error('Error loading dashboard:', err);
        this.generalService.hideDivLoading(); // Ensure loading is hidden on error
        if (err && err.statusCode === 403) { //
          this.router.navigate(['/complete-registration']); //
          this.toastService.showError('Please complete your registration.');
        } else {
          this.toastService.showError('Failed to load dashboard data.');
        }
      }
    });
  }

  /**
   * Determines the API URL and parameters for fetching dashboard data based on user role.
   * @param role The user's role.
   * @param userId The user's ID.
   * @returns An object containing the URL and URL parameters for the dashboard API call.
   */
  private getDashboardConfig(role: IUserRole, userId: string): { url: string; urlParams: any } {
    let url: string = IParents.getDashboard; // Default to parent dashboard
    let urlParams: any = null; // Default to no params

    switch (role) {
      case IUserRole.PARENT:
        url = IParents.getDashboard; //
        urlParams = null; //
        break;
      case IUserRole.STUDENT:
        url = IStudents.getStudentDashboard; //
        urlParams = { StudentId: userId }; //
        break;
      case IUserRole.TEACHER:
        // TODO: Implement teacher dashboard URL and params when available.
        console.warn('Teacher dashboard loading is not yet implemented.');
        break;
      default:
        console.warn(`Unknown user role: ${role}. Cannot determine dashboard configuration.`);
        break;
    }
    return { url, urlParams };
  }

  /**
   * Sets the user's profile and basic info in the AuthStateService.
   * @param response The API response containing profile information.
   */
  private setUserProfileData(response: IGetProfileInfoResponse): void {
    this.authService.setUserProfileInfo(response); //
    this.authService.setUserBasicInfo(response.basicProfileInfoDto as IBasicProfileInfoDto); //
  }
}