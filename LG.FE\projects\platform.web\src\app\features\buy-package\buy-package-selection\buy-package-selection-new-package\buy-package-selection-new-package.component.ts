import { CommonModule, ViewportScroller } from "@angular/common";
import { Component, ChangeDetectionStrategy, DestroyRef, OnDestroy, OnInit, inject, signal, model, computed, WritableSignal, Injector, ChangeDetectorRef } from "@angular/core";
import { Router } from "@angular/router";
import { ITeachingLanguageDto, IPackagePriceDto, IStudentGroupDto, ILanguageLevelsEnum, IBasketItemDto, IAddToBasketResponse, IBasket, IGetPricesResponse, PricesRoutes, IGetAllTeachingLanguagesResponse, IGetStudentDashboardResponse, IStudentTeachingLanguageDto, IApiResponseBase, ISearchStudentDto, IOrderItemTargetTypeEnum, nameOf, IAddToBasketRequest, DefaultGetStudentsRequest } from "SharedModules.Library";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { untilDestroyed } from "SharedModules.Library";
import { Severity } from "SharedModules.Library";
import { HandleApiResponseService } from 'SharedModules.Library';
import { CheckoutService } from "@platform.src/app/core/services/checkout.service";
import { DataApiStateService } from "SharedModules.Library";
import { EventBusService, EmitEvent, Events } from "SharedModules.Library";
import { GeneralService, GenericPrimeDropdownOption, GenericPrimeEnumToDropdownOptionsConfig } from "SharedModules.Library";
import { RegisterService } from "@platform.src/app/core/services/register.service";
import { StudentGroupSelectionDialogComponent } from "@platform.src/app/shared/components/dialogs/student-group-selection-dialog/student-group-selection-dialog.component";
import { EmptyDataImageTextComponent } from "@platform.src/app/shared/components/empty-data-image-text/empty-data-image-text.component";

import { FormFieldValidationMessageComponent } from "SharedModules.Library";
import { PrimeDropdownComponent } from "SharedModules.Library";
import { PrimeStudentGroupSelectionComponent } from "@platform.src/app/shared/components/prime/prime-student-group-selection/prime-student-group-selection.component";
import { PrimeStudentsSelectionComponent } from "@platform.src/app/shared/components/prime/prime-students-selection/prime-students-selection.component";

import { Subscription } from "rxjs";
import { ToastService } from "SharedModules.Library";
import { BuyPackageDurationBoxComponent } from "@platform.src/app/features/buy-package/buy-package-duration-box/buy-package-duration-box.component";
import { PackageExtensionCardComponent } from "@platform.src/app/features/buy-package/buy-package-extension-selection/package-extension-card/package-extension-card.component";
import { BuyPackagePricingBoxComponent } from "@platform.src/app/features/buy-package/buy-package-pricing-box/buy-package-pricing-box.component";
import { BuyPackageCompleteTrialInfoBoxComponent } from "@platform.src/app/features/buy-package/buy-package-selection/buy-package-complete-trial-info-box/buy-package-complete-trial-info-box.component";
import { AuthStateService } from "SharedModules.Library";
import { toObservable } from "@angular/core/rxjs-interop";

const IPackagePriceDtoParamsMap = nameOf<IPackagePriceDto>();

@Component({
  selector: 'app-buy-package-selection-new-package',
  imports: [
    CommonModule,
    ButtonModule,
    DialogModule,
    BuyPackagePricingBoxComponent,
    PrimeDropdownComponent,
    PackageExtensionCardComponent,
    BuyPackageDurationBoxComponent,
    FormFieldValidationMessageComponent,
    EmptyDataImageTextComponent,
    PrimeStudentsSelectionComponent,
    PrimeStudentGroupSelectionComponent,
    BuyPackageCompleteTrialInfoBoxComponent
  ],
  templateUrl: './buy-package-selection-new-package.component.html',
  styleUrl: './buy-package-selection-new-package.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BuyPackageSelectionNewPackageComponent implements OnInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);
  Severity = Severity;
  includedDialogVisible = false;
  duration = 30;
  private readonly injector = inject(Injector);
  public readonly generalService = inject(GeneralService);
  public readonly registerService = inject(RegisterService);
  private readonly cdr = inject(ChangeDetectorRef);
  students = this.generalService.dummyStudents;
  languages: ITeachingLanguageDto[] = [];
  filteredLanguages: ITeachingLanguageDto[] = [];
  studentLevels: GenericPrimeDropdownOption[] = [];
  packages = this.generalService.dummyPackagesPricing;
  teachingLanguageId = signal('');
  numberOfStudents = signal(0);
  filteredLevels: GenericPrimeDropdownOption[] = [];
  resetStudentsSelectionSignal = signal(false);
  resetGroupSelectionSignal = model(false);
  packagePrices = signal<IPackagePriceDto[]>([]);
  extensionSelected = signal(false);
  preselectedStudents = signal<ISearchStudentDto[]>([]);
  preselectedStudentsGroup = signal<IStudentGroupDto[]>([]);
  selectedStudentsGroup = signal<IStudentGroupDto>({} as IStudentGroupDto);
  selectedDuration = signal<number>(30);
  selectedStudent = signal<ISearchStudentDto>({} as ISearchStudentDto);
  selectedPackage = signal<IPackagePriceDto>({} as IPackagePriceDto);
  selectedGroupId = signal('');
  selectedLevel = signal<number | undefined>(ILanguageLevelsEnum.None || undefined);
  selectedTeachingLanguage = signal<ITeachingLanguageDto>({} as ITeachingLanguageDto);
  disableLevelDropdown = signal(false);
  showFreeTrialBanner = signal(false);
  currentStep = signal(1);
  showStudentAlreadyInGroupBanner = signal(false);
  extensionSelected$ = computed(() => this.extensionSelected());
  filteredPackagePrices = computed(() =>
    this.packagePrices().filter(price => price.durationInMinutes === this.selectedDuration())
  );

  // Success state for pricing boxes - tracks which package should show success message
  successTriggerPackageId = signal<string>('');
  // Loading state for pricing boxes - tracks which package should show loading message
  loadingTriggerPackageId = signal<string>('');
  private subscriptions: Subscription[] = [];
  private readonly apiService = inject(HandleApiResponseService);
  private readonly eventBusService = inject(EventBusService);
  private readonly toastService = inject(ToastService);
  private readonly checkoutService = inject(CheckoutService);
  private readonly dataStateService = inject(DataApiStateService);
  students$ = computed(() => this.dataStateService.parentStudents.state() || {});
  studentGroups$ = computed(() => this.dataStateService.parentStudentsGroups.state());
  private readonly authService = inject(AuthStateService);
  private readonly router = inject(Router);
  private readonly viewportScroller = inject(ViewportScroller);

  learningType = signal<'individual' | 'group' | null>(null);

  activeStep = signal(1); // Track current active step
  steps = signal([
    { id: 1, title: 'Choose Your Learning Setup', completed: false },
    { id: 2, title: 'Select Language & Level', completed: false },
    { id: 3, title: 'Select Your Package', completed: false },
    { id: 4, title: 'Confirm Your Order', completed: false }
  ]);

  // Add step navigation methods
  setActiveStep(stepId: number) {
    if (this.canNavigateToStep(stepId)) {
      this.activeStep.set(stepId);
    }
  }

  canNavigateToStep(stepId: number): boolean {
    // Add validation logic for each step
    switch (stepId) {
      case 1:
        return true; // Always accessible
      case 2:
        return this.numberOfStudents() > 0;
      case 3:
        return !this.showFreeTrialBanner() && this.selectedLevel() !== undefined;
      case 4:
        return !this.generalService.isObjectEmpty(this.selectedPackage());
      default:
        return false;
    }
  }

  completeStep(stepId: number) {
    const updatedSteps = this.steps().map(step =>
      step.id === stepId ? { ...step, completed: true } : step
    );
    this.steps.set(updatedSteps);
    if (stepId < 4) {
      this.setActiveStep(stepId + 1);
    }
  }


  ngOnInit(): void {
    this.initEvents();
    this.loadTeachingLanguages();
    this.initializeStudentLevels();
    this.mockSelectionForTesting();
  }

  ngOnDestroy(): void {
    // Manual subscriptions cleanup (for EventBus subscriptions that can't use takeUntilDestroyed)
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
    // HTTP and router subscriptions are automatically cleaned up by takeUntilDestroyed
  }

  /** Handles adding items to the cart */
  addToCartEvent(): void {
    this.eventBusService.emit(new EmitEvent(Events.CartItemAdded, undefined));
    this.addToBasketItem();
  }

  /** Handles selection of a pricing box */
  onPricingBoxSelected(packagePrice: IPackagePriceDto): void {
    console.log('Package selected: ', packagePrice);
    this.selectedPackage.set(packagePrice);
    this.addToCartEvent();
    //     this.toastService.show(
    //       getToastMessage(ToastMessages.CartItemBeforeAdded.success, {
    //     data: ''
    // }));
  }

  /** Checks if a package is selected */
  isSelectedPackage(packagePrice: IPackagePriceDto): boolean {
    const selected = this.selectedPackage();
    return [IPackagePriceDtoParamsMap.durationInMinutes,
    IPackagePriceDtoParamsMap.teachingLanguageId, IPackagePriceDtoParamsMap.oneToOnePricePerLesson,
    IPackagePriceDtoParamsMap.numberOfLessons]
      .every(prop => selected[prop as keyof IPackagePriceDto] === packagePrice[prop as keyof IPackagePriceDto]);
  }

  /** Navigates to the free trial form */
  goToFreeTrialForm(): void {
    this.router.navigate(['/request-new-trial/free-trial-reason']);
  }

  /** Opens the new group dialog */
  openNewGroupDialog(): void {
    this.generalService.openComponent(StudentGroupSelectionDialogComponent, { editMode: false });
  }


  setLearningType(type: 'individual' | 'group') {
    this.learningType.set(type);
    // Reset selections when switching types
    if (type === 'individual') {
      this.resetSelections();
      this.resetSelectedStudentItems(true);
    } else {
      this.resetSelections();
      this.resetSelectedGroupItems(true);
    }
  }

  /** Handles selection of a student */
  onSelectedStudent(student: ISearchStudentDto | ISearchStudentDto[]): void {
    if (this.generalService.isObjectEmpty(student)) { return; }
    const studentInfo = student as ISearchStudentDto;
    // const studentAge = this.generalService.calculateAge(studentInfo.dateOfBirth);
    // console.log(this.generalService.calculateAge(studentInfo.dateOfBirth));
    this.resetSelections();
    this.numberOfStudents.set(1);
    this.resetSelectedGroupItems(true);
    this.resetSelectedStudentItems(false);
    this.preselectedStudentsGroup.set([]);
    this.showFreeTrialBanner.set(false);
    this.selectedGroupId.set('');
    this.selectedStudent.set(student as ISearchStudentDto);
    this.filterStudentLanguages();
    this.loadPackagePrices();
    this.updateCurrentStep();
  }

  /** Handles selection of a student group */
  onSelectedGroup(group: IStudentGroupDto): void {
    if (this.generalService.isObjectEmpty(group)) { return; }
    console.log(group);
    this.resetSelections();
    this.numberOfStudents.set(group.basicProfileInfoDto!.length);
    this.selectedStudentsGroup.set(group);
    this.preselectedStudents.set([]);
    this.selectedGroupId.set(group.id);
    this.selectedStudent.set({} as ISearchStudentDto);
    this.showFreeTrialBanner.set(false);
    this.resetSelectedStudentItems(true);
    this.resetSelectedGroupItems(false);
    this.loadPackagePrices();
  }

  /** Handles selection of a teaching language */
  onSelectedLanguage(language: ITeachingLanguageDto): void {
    console.log(language);
    this.selectedTeachingLanguage.set(language);
    this.teachingLanguageId.set(language.id!);
    this.disableLevelDropdown.set(false);
    this.selectedPackage.set({} as IPackagePriceDto);
    this.showFreeTrialBanner.set(false);
    this.checkStudentLanguages(language.id!);
    this.loadPackagePrices();
    this.updateCurrentStep();
  }

  /** Handles selection of a language level */
  onSelectedLevel(level: number): void {
    this.selectedLevel.set(level);
    this.selectedPackage.set({} as IPackagePriceDto);
    this.loadPackagePrices();
    this.updateCurrentStep();
  }

  /** Handles selection of a duration */
  onSelectedDuration(duration: number): void {
    this.selectedDuration.set(duration);
    this.selectedPackage.set({} as IPackagePriceDto);
  }

  onExtensionSelected(extension: boolean): void {
    this.extensionSelected.set(extension);
  }

  /** Updates the current step based on selections */
  updateCurrentStep(): void {
    if (this.numberOfStudents() === 0) {
      this.currentStep.set(1);
    } else if (this.generalService.isObjectEmpty(this.selectedTeachingLanguage())) {
      this.currentStep.set(2);
    } else {
      this.currentStep.set(3);
    }
  }

  /** Resets various selections and flags */
  private resetSelections(): void {
    this.selectedPackage.set({} as IPackagePriceDto);
    this.selectedTeachingLanguage.set({} as ITeachingLanguageDto);
    this.teachingLanguageId.set('');
    this.packagePrices.set([]);
    this.extensionSelected.set(false);
    this.selectedLevel.set(undefined);
    this.resetSelectedGroupItems(true);
    this.resetSelectedStudentItems(true);
    this.selectedGroupId.set('');
    this.disableLevelDropdown.set(false);
  }

  /** Initializes student levels from enum */
  private initializeStudentLevels(): void {
    const config: GenericPrimeEnumToDropdownOptionsConfig = {
      labelProperty: 'name',
      valueProperty: 'code',
      excludeKeys: ['None', 'All'],
      additionalProperties: {
        description: (key: string) => `Level ${key}`
      }
    };
    this.studentLevels = this.generalService.getDropdownOptionsFromEnum(ILanguageLevelsEnum, config);
  }

  /** Adds the selected item to the basket */
  private addToBasketItem(): void {
    // Trigger loading state for the selected package
    const packageKey = this.createPackageKey(this.selectedPackage());
    this.loadingTriggerPackageId.set(packageKey);

    const basketItem: IAddToBasketRequest = this.createBasketItemDto();
    this.createBasketItemRequest(basketItem);
  }




  /** Creates the basket item DTO */
  private createBasketItemDto(): IAddToBasketRequest {

    const addBasketItemRequest: IAddToBasketRequest = {
      basketItem: {
        targetId: this.selectedGroupId() || this.selectedStudent().userId,
        targetType: this.numberOfStudents() === 1 ? IOrderItemTargetTypeEnum.Student : IOrderItemTargetTypeEnum.Group,
        teachingLanguageId: this.selectedPackage().teachingLanguageId,
        numberOfLessons: this.selectedPackage().numberOfLessons,
        hasAddOnExtension: this.extensionSelected(),
        durationInMinutes: this.selectedPackage().durationInMinutes,
        quantity: null,
        isNewLanguageForStudent: null
      }
    };

    addBasketItemRequest.basketItem.hasFlag = addBasketItemRequest.basketItem.targetType == IOrderItemTargetTypeEnum.Student && this.selectedPackage().hasFlag ? true : false;
    return addBasketItemRequest;
  }

  /** Makes the API request to add the item to the basket */
  private createBasketItemRequest(addToBasketRequest: IAddToBasketRequest): void {
    this.apiService.getApiData<IAddToBasketResponse>(
      { url: IBasket.postAddToBasket, method: 'POST' },
      addToBasketRequest
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: response => this.handleBasketItemSuccess(response),
      error: this.handleBasketItemError.bind(this)
    });
  }

  /** Handles successful addition to the basket */
  private handleBasketItemSuccess(response: IAddToBasketResponse): void {
    const packageKey = this.createPackageKey(this.selectedPackage());

    // Clear loading state
    this.loadingTriggerPackageId.set('');

    // Trigger success message for the selected package
    this.successTriggerPackageId.set(packageKey);

    // Reset the success trigger after a short delay to allow the child component to react
    setTimeout(() => {
      this.successTriggerPackageId.set('');
    }, 50);

    // this.toastService.hide();
    // this.toastService.show(getToastMessage(ToastMessages.CartItemAdded.success, {
    //     data: this.checkoutService.buildBasketItemTitle(basketItem, this.selectedTeachingLanguage())
    // }));
    // this.generalService.cartSidebarVisible.set(true);
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentDashboard,
      {
        role: this.authService.getUserClaims().role,
      }
    ));
    // this.reloadBasketEvent();
    // this.resetSelections();
    // this.resetSelectedStudentItems(true);
    // this.numberOfStudents.set(0);
    this.reloadBasketEvent();
    // this.viewportScroller.scrollToPosition([0, 0]);
  }

  /** Creates a unique key for a package to identify it for success messaging */
  private createPackageKey(packagePrice: IPackagePriceDto): string {
    return `${packagePrice.teachingLanguageId}-${packagePrice.durationInMinutes}-${packagePrice.numberOfLessons}-${packagePrice.oneToOnePricePerLesson}`;
  }

  /** Checks if a package should show success message */
  shouldShowSuccessMessage(packagePrice: IPackagePriceDto): boolean {
    const packageKey = this.createPackageKey(packagePrice);
    return this.successTriggerPackageId() === packageKey;
  }

  /** Checks if a package should show loading message */
  shouldShowLoadingMessage(packagePrice: IPackagePriceDto): boolean {
    const packageKey = this.createPackageKey(packagePrice);
    return this.loadingTriggerPackageId() === packageKey;
  }

  /** Handles errors during addition to the basket */
  private handleBasketItemError(err: IApiResponseBase): void {
    // Clear loading state on error
    this.loadingTriggerPackageId.set('');

    const errorMessage = `${err.messages!.join(', ')}. `;
    this.toastService.show({
      position: 'top-right',
      severity: 'warn',
      summary: 'Cart',
      detail: errorMessage,
    });
  }

  /** Filters available languages based on the selected student */
  private filterStudentLanguages(): void {
    if (this.generalService.isObjectEmpty(this.selectedStudent())) { return; }
    const studentLanguageIds = this.selectedStudent().studentTeachingLanguageDto!.map((lang: IStudentTeachingLanguageDto) => lang.teachingLanguageId);
    this.filteredLanguages = this.languages.filter(language => studentLanguageIds.includes(language.id!));

    const studentLanguageLevels = this.selectedStudent().studentTeachingLanguageDto!.map((lang: IStudentTeachingLanguageDto) => lang.languageLevel);
    this.filteredLevels = this.studentLevels.filter(level => studentLanguageLevels.includes(level.code));
  }

  /** Checks if the selected teaching language is valid for the student */
  private checkStudentLanguages(teachingLanguageId: string): boolean {
    console.log(this.numberOfStudents());
    const studentLanguageIds = this.getStudentLanguageIds(teachingLanguageId);
    const studentLanguageNames = [this.selectedStudentsGroup()?.groupName ?? ''];
    const filteredLanguages = this.getFilteredLanguages(teachingLanguageId, studentLanguageIds, studentLanguageNames);
    const hasLang = filteredLanguages.length > 0 && filteredLanguages[0].id === teachingLanguageId;

    // Check if the selected language has a groupId
    const selectedLanguage = this.selectedStudent().studentTeachingLanguageDto.find((lang: IStudentTeachingLanguageDto) => lang.teachingLanguageId === teachingLanguageId);
    const hasGroup = selectedLanguage?.groupId != null;

    // Show banner if either there's no language match or if the selected language has a groupId
    this.showFreeTrialBanner.set(!hasLang);
    this.showStudentAlreadyInGroupBanner.set(hasGroup);
    if (hasLang) {
      const studentLanguageLevels = this.getStudentLanguageLevels(teachingLanguageId);
      this.filteredLevels = this.getFilteredLevels(studentLanguageLevels);
      this.disableLevelDropdown.set(this.filteredLevels.length > 0);
      this.updateSelectedLevel(this.filteredLevels);
    } else {
      this.resetFilteredLevelsAndSelectedLevel();
    }

    return hasLang;
  }

  /** Gets the IDs of the student's languages */
  private getStudentLanguageIds(teachingLanguageId: string): string[] {
    return this.numberOfStudents() === 1
      ? this.selectedStudent().studentTeachingLanguageDto!.map((lang: IStudentTeachingLanguageDto) => lang.teachingLanguageId)
      : [] as string[];
  }

  /** Filters languages based on the selected teaching language and student */
  private getFilteredLanguages(teachingLanguageId: string, studentLanguageIds: string[], studentLanguageNames: string[]): ITeachingLanguageDto[] {
    return this.numberOfStudents() === 1
      ? this.languages.filter(language => studentLanguageIds.includes(language.id!) && language.id === teachingLanguageId)
      : this.languages.filter(language => studentLanguageNames.includes(language.name!));
  }

  /** Gets the levels of the student's languages */
  private getStudentLanguageLevels(teachingLanguageId: string): ILanguageLevelsEnum[] {
    return this.numberOfStudents() === 1
      ? this.selectedStudent().studentTeachingLanguageDto!
        .filter((lang: IStudentTeachingLanguageDto) => lang.teachingLanguageId === teachingLanguageId)
        .map((lang: IStudentTeachingLanguageDto) => lang.languageLevel)
      : [this.selectedStudentsGroup().groupLevel];
  }

  /** Filters levels based on the student's language levels */
  private getFilteredLevels(studentLanguageLevels: ILanguageLevelsEnum[]): GenericPrimeDropdownOption[] {
    return this.studentLevels.filter(level => studentLanguageLevels.includes(level.code));
  }

  /** Updates the selected level based on filtered levels */
  private updateSelectedLevel(filteredLevels: GenericPrimeDropdownOption[]): void {
    this.selectedLevel.set(filteredLevels.length > 0 ? filteredLevels[0].code : undefined);
  }

  /** Resets filtered levels and selected level */
  private resetFilteredLevelsAndSelectedLevel(): void {
    this.filteredLevels = [];
    this.selectedLevel.set(undefined);
  }

  /** Resets selected student items */
  private resetSelectedStudentItems(bool: boolean): void {
    setTimeout(() => this.resetStudentsSelectionSignal.set(bool), 10);
  }

  /** Resets selected group items */
  private resetSelectedGroupItems(bool: boolean): void {
    setTimeout(() => this.resetGroupSelectionSignal.set(bool), 10);
    // this.toggleResetSignal(this.resetGroupSelectionSignal);
  }

  /** Toggles a reset signal */
  private toggleResetSignal(signal: WritableSignal<boolean>): void {
    setTimeout(() => signal.set(false), 0);
  }

  /** Loads package prices based on selected criteria */
  private loadPackagePrices(): void {
    const { teachingLanguageId, selectedLevel, numberOfStudents } = this;
    if (!teachingLanguageId() || numberOfStudents() === 0) {
      return;
    }

    this.apiService.getApiData<IGetPricesResponse>(
      { url: PricesRoutes.getPrices, method: 'GET' },
      {
        TeachingLanguageId: teachingLanguageId(),
        Level: selectedLevel(),
        NumberOfStudents: numberOfStudents(),
      }
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((response: IGetPricesResponse) => {
        this.packagePrices.set(response.packagePrices);

        if (numberOfStudents() === 1) {
          const studentAge = this.generalService.calculateAge(this.selectedStudent().dateOfBirth);
          this.selectedDuration.set(this.calculateDurationBasedOnAge(studentAge!));
          this.duration = this.selectedDuration();
        }

        this.selectedPackage.set(this.filteredPackagePrices()[2]);
      });
  }

  /** Initializes event subscriptions */
  private initEvents(): void {
    const request = new DefaultGetStudentsRequest({
      pageSize: 100
    });
    const cleanRequest = this.generalService.cleanRequestForApi(request);
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudents, cleanRequest));
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudentsGroups, undefined));
    const studentGroupAddedSubscription = this.eventBusService.on(Events.StudentGroupAdded, (payload) => {
      console.log(`Customer Selected: ${payload.name}`);
    });
    this.subscriptions.push(studentGroupAddedSubscription);
  }

  /** Loads teaching languages from the API */
  private loadTeachingLanguages(): void {
    this.apiService.getTeachingLanguages()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (teachingLanguageResponse: IGetAllTeachingLanguagesResponse) => {
          this.languages = teachingLanguageResponse.teachingLanguages;
        }
      });
  }

  /** Reloads the basket event */
  private reloadBasketEvent(): void {
    this.eventBusService.emit(new EmitEvent(Events.StateLoadGetBasket, {
      parentId: this.authService.getUserClaims().id
    }));
  }

  private calculateDurationBasedOnAge(age: number): number {
    const ageDurationMap: { min: number; max: number; duration: number }[] = [
      { min: 2, max: 4, duration: 15 },
      { min: 5, max: 8, duration: 30 },
      { min: 9, max: 17, duration: 60 }
    ];

    const ageRange = ageDurationMap.find(range => age >= range.min && age <= range.max);
    return ageRange?.duration ?? 30; // Default to 30 minutes if age is outside defined ranges
  }


  // ... existing code ...

  /** 
   * Mock method for testing the selection flow
   * This simulates selecting a student, language, and level to generate filteredPackagePrices
   */
  mockSelectionForTesting(): void {
    // Mock data

    // 1. Set learning type to individual
    this.setLearningType('individual');


    // const getUserProfileInfo = toObservable(this.students$, {
    //   injector: this.injector
    // }).pipe(this.untilDestroyed()).subscribe({
    //   next: (state) => {
    //     if (state.loading) {
    //       return;
    //     }
    //     if (state.data) {
    //       const student: ISearchStudentDto = state.data.pageData[0];
    //       console.log('User Profile Info', state.data.pageData[0]);
    //       setTimeout(() => {
    //         this.selectedStudent.set(state.data.pageData[0]);
    //         this.onSelectedStudent(state.data.pageData[0]);
    //         this.onSelectedLanguage(
    //           {
    //             id: student.studentTeachingLanguageDto[0].teachingLanguageId,
    //             name: student.studentTeachingLanguageDto[0].teachingLanguageName,
    //             order: 3,
    //             isActive: true
    //           }
    //         );
    //         this.teachingLanguageId.set(student.studentTeachingLanguageDto[0].teachingLanguageId);
    //         this.onSelectedLevel(ILanguageLevelsEnum.B1);
    //         this.cdr.detectChanges();
    //         this.cdr.markForCheck();
    //       }, 1000)

    //     }
    //   }
    // });
    // // 3. Mock languages
    // this.languages = [mockLanguage];

    // // 4. Select student
    // this.onSelectedStudent(mockStudent);

    // // 5. Mock the API call for loadPackagePrices
    // // Instead of making the actual API call, we'll directly set the package prices
    // this.packagePrices.set(mockPackagePrices);

    // // 6. Select language
    // this.onSelectedLanguage(mockLanguage);

    // // 7. Select level
    // this.onSelectedLevel(ILanguageLevelsEnum.B1);

    // // 8. Select duration (optional, as it's already set in onSelectedStudent based on age)
    // this.onSelectedDuration(30);

    // // 9. Select a package
    // this.onPricingBoxSelected(mockPackagePrices[2]);

    console.log('Mock selection completed');
    console.log('Selected student:', this.selectedStudent());
    console.log('Selected language:', this.selectedTeachingLanguage());
    console.log('Selected level:', this.selectedLevel());
    console.log('Filtered package prices:', this.filteredPackagePrices());
  }

  // ... existing code ...
}
